import crypto from 'crypto';

const ALGORITHM = 'aes-256-gcm'; // Recommended algorithm
const IV_LENGTH = 16; // For GCM, IV is typically 12 bytes, but 16 is also common for AES blocks
const AUTH_TAG_LENGTH = 16; // GCM auth tag length

// Ensure your key is 32 bytes (256 bits) for AES-256
const getKey = (): Buffer => {
    const secret = process.env.IMAGE_URL_ENCRYPTION_KEY;
    if (!secret || secret.length < 32) {
        // In a real app, you'd want a robust way to handle this,
        // perhaps a default key for dev but a strict requirement for prod.
        // Forcing a 32-byte key from a potentially shorter secret:
        console.warn("IMAGE_URL_ENCRYPTION_KEY is missing or too short. Using a derived key (not recommended for production). Ensure it's at least 32 characters.");
        return crypto.createHash('sha256').update(String(secret || 'default-secret-for-dev-only-32chars')).digest();
    }
    return Buffer.from(secret.slice(0, 32), 'utf-8'); // Use first 32 chars
};

export const encryptUrl = (text: string): string | null => {
    try {
        const key = getKey();
        const iv = crypto.randomBytes(IV_LENGTH);
        const cipher = crypto.createCipheriv(ALGORITHM, key, iv);

        let encrypted = cipher.update(text, 'utf8', 'hex');
        encrypted += cipher.final('hex');

        const authTag = cipher.getAuthTag(); // Get the auth tag

        // Prepend IV and authTag to the encrypted text (hex encoded)
        return iv.toString('hex') + authTag.toString('hex') + encrypted;
    } catch (error) {
        console.error("Encryption failed:", error);
        return null;
    }
};

export const decryptUrl = (encryptedText: string): string | null => {
    try {
        const key = getKey();
        const ivHex = encryptedText.slice(0, IV_LENGTH * 2);
        const authTagHex = encryptedText.slice(IV_LENGTH * 2, (IV_LENGTH + AUTH_TAG_LENGTH) * 2);
        const ciphertextHex = encryptedText.slice((IV_LENGTH + AUTH_TAG_LENGTH) * 2);

        const iv = Buffer.from(ivHex, 'hex');
        const authTag = Buffer.from(authTagHex, 'hex');

        const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
        decipher.setAuthTag(authTag); // Set the auth tag for GCM

        let decrypted = decipher.update(ciphertextHex, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
    } catch (error) {
        console.error("Decryption failed:", error);
        return null;
    }
};
