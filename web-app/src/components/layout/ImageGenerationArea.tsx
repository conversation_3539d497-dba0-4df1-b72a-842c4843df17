"use client";

import { ImagePreviewModal } from "@/components/modals/ImagePreviewModal"; // Import the modal
import { GenerationForm } from "@/components/sections/GenerationForm";
import { HistoryItem, HistorySection } from "@/components/sections/HistorySection";
import { useState } from "react";

export const ImageGenerationArea = () => {
    const [viewMode, setViewMode] = useState<"singleColumn" | "twoColumn">("singleColumn");
    const [historyItems, setHistoryItems] = useState<HistoryItem[]>([]);
    const [currentGeneration, setCurrentGeneration] = useState<{ isLoading: boolean; estimatedTime?: string; id: string } | null>(null);
    const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false); // State for modal visibility
    const [selectedImageForPreview, setSelectedImageForPreview] = useState<HistoryItem | null>(null); // State for selected image

    const handleImageClickInHistory = (item: HistoryItem) => {
        setSelectedImageForPreview(item);
        setIsPreviewModalOpen(true);
    };

    const handleClosePreviewModal = () => {
        setIsPreviewModalOpen(false);
        setSelectedImageForPreview(null);
    };

    const handleGenerationStart = () => {
        setViewMode("twoColumn");
        setCurrentGeneration({ isLoading: true, estimatedTime: "约 30 秒", id: `gen-${Date.now()}` });
        // Add a placeholder to history if desired, or wait for result
    };

    const handleGenerationComplete = (newImage: {
        imageUrl: string;
        prompt?: string;
        seed?: string;
        width?: number;
        height?: number;
        aspectRatioSelected?: string; // Added from GenerationForm
    }) => {
        const newItem: HistoryItem = {
            id: currentGeneration?.id || `hist-${Date.now()}`,
            imageUrl: newImage.imageUrl,
            prompt: newImage.prompt || "N/A",
            timestamp: new Date().toISOString(),
            seed: newImage.seed,
            width: newImage.width,
            height: newImage.height,
            aspectRatioValue: newImage.aspectRatioSelected, // Save the selected aspect ratio
        };
        setHistoryItems(prevItems => [newItem, ...prevItems]);
        setCurrentGeneration(null);
    };

    const handleGenerationError = (error: Error) => {
        console.error("Generation failed in parent:", error);
        // Potentially show an error message in history or a toast
        setCurrentGeneration(null); // Clear loading state
    };

    // Determine the container class for the grid.
    // In singleColumn view, it can take the full width available to ImageGenerationArea.
    // In twoColumn view, we apply max-w-5xl and mx-auto to the grid itself,
    // so the two columns are within this constrained width.
    // The GenerationForm itself might have its own max-width for very wide screens in single column.
    // const gridContainerClass = viewMode === "twoColumn"
    //     ? "md:grid-cols-2 max-w-5xl mx-auto" // Constrain width in two-column
    //     : "grid-cols-1"; // Takes full available width in single-column
    // Switching to Flexbox based on discussion
    // Estimate a suitable fixed height for the two-column row.
    // This might need adjustment based on actual content height of GenerationForm.
    // const twoColumnRowHeight = "h-[calc(35vh+12rem)]"; // Removed fixed row height

    return (
        // This outer div is w-full, allowing it to respect the parent's width constraints from page.tsx
        // The parent in page.tsx (main > div) already has max-w-5xl mx-auto, so ImageGenerationArea will adhere to that.
        <div className={`w-full transition-all duration-300 ease-in-out`}>
            {/* Flex layout with top alignment since history section now has fixed height */}
            <div className={`flex flex-row gap-8 items-start`}>

                {/* Left Column: GenerationForm */}
                <div
                    className={`transition-all duration-300 ease-in-out ${viewMode === "twoColumn"
                        ? "w-2/5"
                        : "w-full"   // In single-column mode, take full width, height is auto
                        }`}
                >
                    <GenerationForm
                        onGenerationStart={handleGenerationStart}
                        onGenerationComplete={handleGenerationComplete}
                        onGenerationError={handleGenerationError}
                        currentViewMode={viewMode}
                    />
                </div>

                {/* Right Column: HistorySection, only in two-column mode */}
                {viewMode === "twoColumn" && (
                    // Fixed height container for history section to prevent it from growing with left column
                    <div className="w-3/5 border border-border rounded-lg shadow-xl p-4 sm:p-6 md:p-8 bg-card flex flex-col" style={{ height: 'calc(35vh + 12rem)', maxHeight: '80vh' }}>
                        <HistorySection
                            items={historyItems}
                            currentGeneration={currentGeneration}
                            onImageClick={handleImageClickInHistory} // Pass the click handler
                        />
                    </div>
                )}
            </div>
            {/* No separate single-column history display as per user confirmation */}

            <ImagePreviewModal
                isOpen={isPreviewModalOpen}
                onClose={handleClosePreviewModal}
                item={selectedImageForPreview}
            />
        </div>
    );
};
