"use client";

import { HistoryItem } from "@/components/sections/HistorySection"; // Assuming HistoryItem is exported here
import { But<PERSON> } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle
} from "@/components/ui/dialog";
import Image from 'next/image';


interface ImagePreviewModalProps {
    isOpen: boolean;
    onClose: () => void;
    item: HistoryItem | null;
}

// Placeholder Icons
const DownloadIcon = () => <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" y1="15" x2="12" y2="3"></line></svg>;
const CloseIcon = () => <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>;

// getAspectRatioClass function is removed as per the new strategy
// const getAspectRatioClass = (aspectRatioValue?: string): string => { ... };

export const ImagePreviewModal = ({ isOpen, onClose, item }: ImagePreviewModalProps) => {
    // console.log("ImagePreviewModal item:", item); // Removed debug log
    if (!item) return null;

    // imageContainerAspectRatioClass is no longer used
    // const imageContainerAspectRatioClass = getAspectRatioClass(item.aspectRatioValue);

    const handleDownload = () => {
        const link = document.createElement('a');
        link.href = item.imageUrl;
        const promptPart = item.prompt ? item.prompt.substring(0, 30).replace(/\s+/g, '_') : 'image';
        const timestampPart = new Date(item.timestamp).toISOString().split('T')[0];
        link.download = `LuckyX-AI-${promptPart}-${timestampPart}.jpg`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[90vw] md:max-w-[85vw] lg:max-w-[80vw] xl:max-w-[75vw] max-h-[95vh] flex flex-col bg-card border-border p-0 [&>button]:hidden">
                <DialogHeader className="flex-shrink-0 p-4 border-b border-border relative">
                    <DialogTitle className="text-sm font-normal text-foreground whitespace-normal max-h-[8vh] overflow-y-auto pr-8">
                        {item.prompt || "图像详情"}
                    </DialogTitle>
                    {/* Custom close button with same style as download button */}
                    <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="absolute top-4 right-4 h-8 w-8 bg-white/90 hover:bg-white text-gray-700 rounded-full opacity-70 hover:opacity-100 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl z-10"
                        onClick={onClose}
                        aria-label="Close modal"
                    >
                        <CloseIcon />
                    </Button>
                </DialogHeader>

                <div className="flex justify-center items-center p-0 w-full" style={{ maxHeight: '85vh', height: '85vh' }}>
                    {/* Wrapper for image with relative positioning for buttons */}
                    <div className="relative w-full h-full flex justify-center items-center">
                        {/* Using Next.js Image component for optimization */}
                        <Image
                            src={item.imageUrl}
                            alt={item.prompt || "Generated image preview"}
                            width={1024}
                            height={1024}
                            className="object-contain rounded"
                            style={{
                                display: 'block',
                                width: 'auto',
                                height: 'auto',
                                maxWidth: '80vw',
                                maxHeight: '80vh',
                                minWidth: '600px',
                                minHeight: '400px'
                            }}
                            sizes="(max-width: 768px) 90vw, (max-width: 1024px) 85vw, (max-width: 1280px) 80vw, 75vw"
                            priority
                        />

                        {/* Download button - bottom right corner of image */}
                        <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="absolute bottom-2 right-2 h-8 w-8 bg-white/90 hover:bg-white text-gray-700 rounded-full opacity-70 hover:opacity-100 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl z-10"
                            onClick={handleDownload}
                            aria-label="Download image"
                        >
                            <DownloadIcon />
                        </Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
};
