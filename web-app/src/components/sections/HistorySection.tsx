"use client";

import Image from 'next/image'; // For optimized images

// Define the type for a history item
export interface HistoryItem {
    id: string;
    imageUrl: string;
    prompt: string;
    timestamp: string; // ISO string
    seed?: string;
    width?: number;
    height?: number;
    aspectRatioValue?: string; // e.g., "square-hd", "portrait-3-4"
}

interface CurrentGeneration {
    isLoading: boolean;
    estimatedTime?: string;
    id: string;
}

interface HistorySectionProps {
    items?: HistoryItem[];
    currentGeneration?: CurrentGeneration | null;
    onImageClick?: (item: HistoryItem) => void; // Callback for when an image is clicked
}

// Updated LoadingSpinner to match the reference image style
const LoadingSpinner = ({ estimatedTime }: { estimatedTime?: string }) => (
    <div className="flex flex-col items-center justify-center p-6 rounded-lg bg-[#3a302a] text-white/80 aspect-square"> {/* Adjusted background and text color */}
        <svg className="animate-spin h-10 w-10 text-[#e89f5a]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"> {/* Adjusted color and size */}
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="3"></circle> {/* Adjusted strokeWidth */}
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        {estimatedTime && <p className="mt-4 text-sm">预计时间：{estimatedTime}</p>}
        {/* Placeholder for the "speed up" button if needed later
        <button className="mt-4 px-4 py-2 bg-[#e89f5a] text-black rounded-md text-sm font-medium">
            ⚡️ 生成速度快 5 倍
        </button> 
        */}
    </div>
);


export const HistorySection = ({ items = [], currentGeneration, onImageClick }: HistorySectionProps) => {
    // If loading, the currentGeneration placeholder will be the first item visually.
    // The actual history items follow.
    return (
        <div className="w-full h-full flex flex-col"> {/* Ensure it takes full height of its container */}
            <h3 className="mb-4 text-lg font-semibold text-foreground flex-shrink-0">
                历史记录
            </h3>
            <div className="flex-grow overflow-y-auto pr-1 pb-1"> {/* Adjusted padding for scrollbar */}
                <div className="grid grid-cols-3 gap-3"> {/* Fixed 3 columns, gap-3 */}

                    {currentGeneration?.isLoading && (
                        // Loading spinner takes one grid cell
                        <div className="border border-dashed border-primary/30 rounded-lg bg-primary/5">
                            <LoadingSpinner estimatedTime={currentGeneration.estimatedTime} />
                        </div>
                    )}

                    {items.map((item) => (
                        <div
                            key={item.id}
                            className="rounded-lg p-1.5 hover:bg-white/5 transition-colors cursor-pointer flex flex-col" // Simplified item styling
                            onClick={() => onImageClick?.(item)}
                        >
                            <div className="aspect-square w-full relative overflow-hidden rounded bg-black/20"> {/* Darker bg for placeholder */}
                                <Image
                                    src={item.imageUrl}
                                    alt={item.prompt || 'Generated image'}
                                    fill
                                    sizes="(max-width: 768px) 33vw, (max-width: 1024px) 20vw, 15vw" // Adjusted sizes for 3 columns
                                    className="object-contain"
                                    priority={items.indexOf(item) < 6} // Prioritize more images if 3 columns
                                />
                            </div>
                            <p className="mt-1 text-[10px] text-muted-foreground/80 truncate" title={item.prompt}>
                                {item.prompt || "无提示词"}
                            </p>
                            <p className="text-[9px] text-muted-foreground/60 mt-0.5">
                                {new Date(item.timestamp).toLocaleString()}
                            </p>
                        </div>
                    ))}
                </div>

                {items.length === 0 && !currentGeneration?.isLoading && (
                    // This message will appear if the grid is empty.
                    // It might need to be styled to span all columns or be centered differently if grid is active.
                    // For simplicity, let's assume if grid is empty, this fills the space.
                    <div className="p-6 text-center text-muted-foreground h-full flex flex-col justify-center items-center">
                        <p>暂无历史记录。</p>
                        <p className="text-sm">生成的图像将显示在这里。</p>
                    </div>
                )}
            </div>
        </div>
    );
};
