import { encryptUrl } from '@/lib/cryptoUtils';
import { fal } from '@fal-ai/client';
import { NextRequest, NextResponse } from 'next/server';

// Configure fal.ai client
if (!process.env.FAL_KEY) {
    console.warn("FAL_KEY environment variable is not set. Fal.ai calls will likely fail.");
}
fal.config({
    credentials: process.env.FAL_KEY || "FAL_KEY_NOT_SET",
});

interface GenerateRequestBody {
    prompt?: string;
    aspectRatio?: string; // Changed from aspect_ratio to match frontend state variable
    outputQuality?: 'standard' | 'high';
    uploadedImage?: string; // Changed from image_url to match frontend state variable
}

const MODEL_IDS = {
    textToImage: {
        standard: 'fal-ai/hidream-i1-fast',
        high: 'fal-ai/hidream-i1-dev',
    },
    imageToImage: {
        standard: 'fal-ai/hidream-e1-full',
        high: 'fal-ai/step1x-edit',
    },
};

const mapAspectRatioToFalImageSize = (aspectRatio?: string): string => {
    const mapping: Record<string, string> = {
        "square": "square",
        "square-hd": "square_hd",
        "portrait-3-4": "portrait_4_3",
        "portrait-9-16": "portrait_16_9",
        "landscape-4-3": "landscape_4_3",
        "landscape-16-9": "landscape_16_9",
    };
    return aspectRatio ? (mapping[aspectRatio] || "square_hd") : "square_hd";
};

export async function POST(request: NextRequest) {
    console.log("[API_V1_IMAGE_GENERATE] Request received");
    try {
        const body = await request.json() as GenerateRequestBody;
        console.log("[API_V1_IMAGE_GENERATE] Request body:", JSON.stringify(body, null, 2));

        const {
            prompt,
            aspectRatio,
            outputQuality = 'standard',
            uploadedImage,
        } = body;

        let modelId: string;
        const falInputs: any = {
            enable_safety_checker: false,
            image_size: mapAspectRatioToFalImageSize(aspectRatio),
        };

        if (uploadedImage) {
            modelId = MODEL_IDS.imageToImage[outputQuality];
            falInputs.image_url = uploadedImage; // Fal.ai SDK can handle data URIs
            if (modelId === MODEL_IDS.imageToImage.high) { // step1x-edit
                falInputs.prompt = prompt || "Make it look better";
            } else { // hidream-e1-full
                falInputs.edit_instruction = prompt || "Enhance this image";
            }
        } else {
            if (!prompt || prompt.trim() === "") {
                return NextResponse.json({ success: false, error: { code: 'VALIDATION_ERROR', message: "Prompt is required for text-to-image generation." } }, { status: 400 });
            }
            modelId = MODEL_IDS.textToImage[outputQuality];
            falInputs.prompt = prompt.trim();
        }

        if (!modelId) {
            return NextResponse.json({ success: false, error: { code: 'VALIDATION_ERROR', message: "Invalid model selection based on parameters." } }, { status: 400 });
        }
        console.log(`[API_V1_IMAGE_GENERATE] Using modelId: ${modelId}, inputs: ${JSON.stringify(falInputs, null, 2)}`);

        // Call Fal.ai synchronously
        // console.log("[API_V1_IMAGE_GENERATE] Calling fal.run()...");
        const falResult: any = await fal.run(modelId, { input: falInputs });
        console.log("[API_V1_IMAGE_GENERATE] fal.run() response:", JSON.stringify(falResult, null, 2));

        // Corrected check for Fal.ai response structure
        if (falResult.error || !falResult.data || !falResult.data.images || !Array.isArray(falResult.data.images) || falResult.data.images.length === 0) {
            console.error("[API_V1_IMAGE_GENERATE] Fal.ai generation error or no images in response data:",
                falResult.error || "No images array in falResult.data or array is empty",
                "Full FalResult:", JSON.stringify(falResult, null, 2));
            return NextResponse.json({
                success: false,
                error: {
                    code: 'GENERATION_FAILED',
                    messagee: 'System is busy, please try again later.',
                    // message: (falResult.error?.message || falResult.reason || "Image generation failed at Fal.ai or no image data returned.")
                }
            }, { status: 502 });
        }

        const imageOutput = falResult.data.images[0]; // Corrected path
        const originalFalImageUrl = imageOutput.url;
        console.log("[API_V1_IMAGE_GENERATE] Original Fal image URL:", originalFalImageUrl);

        if (!originalFalImageUrl) {
            console.error("[API_V1_IMAGE_GENERATE] No image URL in Fal.ai imageOutput.");
            return NextResponse.json({ success: false, error: { code: 'GENERATION_FAILED', message: "System is busy, please try again later." } }, { status: 502 });
        }

        const encryptedUrl = encryptUrl(originalFalImageUrl);
        if (!encryptedUrl) {
            console.error("[API_V1_IMAGE_GENERATE] Failed to encrypt image URL for:", originalFalImageUrl);
            return NextResponse.json({ success: false, error: { code: 'ENCRYPTION_ERROR', message: "Failed to secure image URL." } }, { status: 500 });
        }
        console.log("[API_V1_IMAGE_GENERATE] Encrypted URL part:", encryptedUrl.substring(0, 50) + "...");

        const proxiedImageUrl = `/api/v1/image/view/${encodeURIComponent(encryptedUrl)}`;

        const responseData = {
            success: true,
            data: {
                imageUrl: proxiedImageUrl,
                originalPrompt: prompt || falInputs.edit_instruction || falResult.data.prompt || "N/A", // Attempt to get prompt from falResult.data as well
                dimensions: {
                    width: imageOutput.width,
                    height: imageOutput.height,
                },
                requestDetails: {
                    aspectRatio: aspectRatio,
                    outputQuality: outputQuality,
                },
                // seed is not included as per decision
            }
        };

        return NextResponse.json(responseData);

    } catch (error: any) {
        console.error("[API_V1_IMAGE_GENERATE_ERROR]", error);
        // Distinguish between client errors (e.g., bad JSON) and server errors
        if (error instanceof SyntaxError) {
            return NextResponse.json({ success: false, error: { code: 'BAD_REQUEST', message: "Invalid JSON payload." } }, { status: 400 });
        }
        return NextResponse.json({ success: false, error: { code: 'SERVER_ERROR', message: "An unexpected server error occurred." } }, { status: 500 });
    }
}
