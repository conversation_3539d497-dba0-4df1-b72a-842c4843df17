// src/app/api/v1/image/view/[encryptedSource]/route.ts
import { decryptUrl } from '@/lib/cryptoUtils';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(
    request: NextRequest,
    context: { params: Promise<{ encryptedSource?: string }> } // params is now a Promise in Next.js 15
) {
    // Await params before accessing its properties
    const params = await context.params;

    // Ensure params and encryptedSource exist before trying to use them
    if (!context || !params || typeof params.encryptedSource !== 'string') {
        console.error("[API_V1_IMAGE_VIEW_ERROR] Invalid or missing encryptedSource in params:", params);
        return NextResponse.json({ error: 'encryptedSource parameter is missing or invalid in path' }, { status: 400 });
    }

    const encryptedSourceUrl: string = params.encryptedSource;

    try {
        console.log(`[API_V1_IMAGE_VIEW] Received encryptedSourceUrl: ${encryptedSourceUrl.substring(0, 50)}...`);
        const originalFalImageUrl = decryptUrl(encryptedSourceUrl);

        if (!originalFalImageUrl) {
            console.error(`[API_V1_IMAGE_VIEW_ERROR] Failed to decrypt sourceUrl: ${encryptedSourceUrl.substring(0, 50)}...`);
            return NextResponse.json({ error: 'Invalid or undecryptable sourceUrl' }, { status: 400 });
        }
        console.log(`[API_V1_IMAGE_VIEW] Decrypted to: ${originalFalImageUrl.substring(0, 50)}...`);


        const imageResponseFromFal = await fetch(originalFalImageUrl);
        console.log(`[API_V1_IMAGE_VIEW] Fetched from Fal, status: ${imageResponseFromFal.status}`);


        if (!imageResponseFromFal.ok) {
            const errorText = await imageResponseFromFal.text();
            console.error(`[API_V1_IMAGE_VIEW_ERROR] Failed to fetch from original source. Status: ${imageResponseFromFal.status}. Body: ${errorText}`);
            return NextResponse.json(
                { error: 'Failed to fetch image from original source', status: imageResponseFromFal.status, details: errorText },
                { status: imageResponseFromFal.status }
            );
        }

        const imageBuffer = await imageResponseFromFal.arrayBuffer();
        const contentType = imageResponseFromFal.headers.get('content-type') || 'application/octet-stream';
        console.log(`[API_V1_IMAGE_VIEW] Successfully fetched image. Content-Type: ${contentType}, Size: ${imageBuffer.byteLength}`);


        return new NextResponse(imageBuffer, {
            status: 200,
            headers: {
                'Content-Type': contentType,
                'Cache-Control': 'public, max-age=3600, immutable',
            },
        });

    } catch (error: any) {
        console.error(`[API_V1_IMAGE_VIEW_ERROR] General error for EncryptedSource: ${encryptedSourceUrl.substring(0, 50)}...`, error);
        return NextResponse.json({ error: 'Failed to proxy image', details: error.message }, { status: 500 });
    }
}
