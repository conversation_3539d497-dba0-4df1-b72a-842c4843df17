import type { User as NextA<PERSON>User, Profile } from "next-auth";
import NextAuth, { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";

// Extend Profile type to include Google-specific properties
interface GoogleProfile extends Profile {
    picture?: string;
    image?: string;
}

// Our user interface that will be passed to session
interface Extended<PERSON><PERSON> extends NextAuthUser {
    id: string;
}

export const authOptions: NextAuthOptions = {
    providers: [
        GoogleProvider({
            clientId: process.env.GOOGLE_CLIENT_ID as string,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
        }),
    ],
    // No adapter - using JWT strategy only
    session: {
        strategy: "jwt", // Store the user's session in a JWT, not a database
        maxAge: 30 * 24 * 60 * 60, // 30 days
    },
    callbacks: {
        // Called when JWT is created / updated
        async jwt({ token, user, account, profile }) {
            // Initial sign-in: token gets data from the OAuth provider response
            if (account && profile) {
                const googleProfile = profile as GoogleProfile;
                // Google profile specific - handle safely
                if (profile.sub) {
                    token.sub = profile.sub; // Existing sub property from JWT
                }

                // Only set these if they exist in the profile
                if (typeof profile.email === 'string') token.email = profile.email;
                if (typeof profile.name === 'string') token.name = profile.name;

                // Handle Google profile picture
                // Google OAuth often returns the picture URL with a default size parameter
                // We remove any size restrictions to get the full-resolution image
                if (googleProfile.picture && typeof googleProfile.picture === 'string') {
                    // Remove size limitations from Google profile picture URL
                    // e.g., convert https://lh3.googleusercontent.com/a/...=s96-c to https://lh3.googleusercontent.com/a/...
                    let pictureUrl = googleProfile.picture;

                    // Remove any size specifiers like =s96-c, =s128, etc.
                    if (pictureUrl.includes('googleusercontent.com')) {
                        pictureUrl = pictureUrl.split('=')[0];
                    }

                    token.picture = pictureUrl;
                } else if (googleProfile.image && typeof googleProfile.image === 'string') {
                    token.picture = googleProfile.image;
                }
            }
            return token;
        },

        // Called whenever session is checked (Client-side: useSession, Server-side: getSession)
        async session({ session, token }) {
            // Add token properties to the session.user
            if (session.user) {
                // Type assertion for TS
                const user = session.user as ExtendedUser;

                // Use token.sub (standard JWT claim for subject/user ID)
                // This should always be present from OAuth providers
                if (token.sub) {
                    user.id = token.sub;
                } else {
                    user.id = "anonymous"; // Fallback if somehow missing
                }

                // Copy other user properties if they exist
                if (typeof token.picture === 'string') user.image = token.picture;
                if (typeof token.name === 'string') user.name = token.name;
                if (typeof token.email === 'string') user.email = token.email;
            }
            return session;
        },
    },
    secret: process.env.NEXTAUTH_SECRET,
    // 自定义页面
    pages: {
        signIn: '/auth/loading',
        error: '/auth/error',
    },
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
